package subsystems

import "path"

type MemorySubSystem struct {
}

func (s *MemorySubSystem) Set(containerName string, res *ResourceConfig) error {
	subsysCgroupPath, err := GetCgroupPath(s.Name(), containerName, true)
	if err != nil {
		return fmt.Errorf("get cgroup path for memory subsystem error: %v", err)
	}

	if res.MemoryLimit == "" {
		return nil
	}

	if err := writeSubsystemFile(path.Join(subsysCgroupPath, "memory.limit_in_bytes"), []byte(res.MemoryLimit), 0644); err != nil {
		return fmt.Errorf("write memory.limit_in_bytes file error: %v", err)
	}
	return nil
}

func (s *MemorySubSystem) Remove(containerName string) error {
	if err := removeCgroupAtPath(s.Name(), containerName); err != nil {
		return fmt.Errorf("remove cgroup at path for memory subsystem error: %v", err)
	}
	return nil
}

func (s *MemorySubSystem) Apply(containerName string, pid int) error {
	if err := applyPidToCgroup(s.Name(), containerName, pid, 0644); err != nil {
		return fmt.Errorf("apply pid to cgroup for memory subsystem error: %v", err)
	}
	return nil
}

// Name return the name of memory subsystem
func (s *MemorySubSystem) Name() string {
	return "memory"
}
