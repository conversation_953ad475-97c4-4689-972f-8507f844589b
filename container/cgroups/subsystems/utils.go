package subsystems

import (
	"bufio"
	"docker/config"
	"fmt"
	"io/fs"
	"io/ioutil"
	"os"
	"path"
	"strconv"
	"strings"
)

// FindCgroupMountPoint find the cgroup path where the specified subsystem mounted
func FindCgroupMountPoint(subsystem string) (string, error) {
	f, err := os.Open(config.ProcessMountInfoPath)
	if err != nil { // mountinfo open failed
		return "", err
	}
	defer f.Close()

	scanner := bufio.NewScanner(f)
	for scanner.Scan() {
		txt := scanner.Text()
		fields := strings.Split(txt, " ")
		// find the row of memory cgroup
		for _, opt := range strings.Split(fields[len(fields)-1], ",") {
			if opt == subsystem {
				return fields[4], nil
			}
		}
	}
	// check scanner errors
	if err = scanner.Err(); err != nil {
		return "", err
	}

	return "", fmt.Errorf("cgroup %s not found", subsystem)
}

// GetCgroupPath get the absolute path of the specified cgroup
// auto create when path not exists and autoCreate set to true
func GetCgroupPath(subsystem string, containerName string, autoCreate bool) (string, error) {
	cgroupRoot, err := FindCgroupMountPoint(subsystem)
	if err != nil {
		return "", err
	}
	cgroupPath := path.Join(cgroupRoot, containerName)
	_, err = os.Stat(cgroupPath)
	if err != nil {
		if !autoCreate || !os.IsNotExist(err) {
			return "", fmt.Errorf("cgroup path %s error: %v", cgroupPath, err)
		}

		if err = os.MkdirAll(cgroupPath, 0755); err != nil {
			return "", fmt.Errorf("create cgroup path %s error: %v", cgroupPath, err)
		}
	}

	return cgroupPath, nil
}

// do subsystem file write opr
func writeSubsystemFile(filePath string, content []byte, fileMode fs.FileMode) error {
	// write memory limit for the cgroup
	err := ioutil.WriteFile(filePath, content, fileMode)
	if err != nil {
		return fmt.Errorf("cgroup %s write fail: %v", filePath, err)
	}

	return nil
}

// delete a cgroup node in hierarchy
func removeCgroupAtPath(subsysName, containerName string) error {
	subsysCgroupPath, err := GetCgroupPath(subsysName, containerName, false)
	if err != nil {
		return fmt.Errorf("get cgroup path for %s subsystem and container %s error: %v", subsysName, containerName, err)
	}

	if err := os.RemoveAll(subsysCgroupPath); err != nil {
		return fmt.Errorf("remove cgroup path %s error: %v", subsysCgroupPath, err)
	}
	return nil
}

// write the pid to the cgroupPath/cgroup.procs
func applyPidToCgroup(subsysName, containerName string, pid int, perm fs.FileMode) error {
	subsysCgroupPath, err := GetCgroupPath(subsysName, containerName, false)
	if err != nil {
		return fmt.Errorf("get cgroup path for %s subsystem and container %s error: %v", subsysName, containerName, err)
	}

	if err := ioutil.WriteFile(
		path.Join(subsysCgroupPath, "tasks"), []byte(strconv.Itoa(pid)), perm,
	); err != nil {
		return fmt.Errorf("write pid %d to cgroup tasks file %s error: %v", pid, path.Join(subsysCgroupPath, "tasks"), err)
	}
	return nil
}
