package subsystems

import "path"

type CpusetSubSystem struct {
}

func (s *CpusetSubSystem) Set(containerName string, res *ResourceConfig) error {
	subsysCgroupPath, err := GetCgroupPath(s.Name(), containerName, true)
	if err != nil {
		return fmt.Errorf("get cgroup path for cpuset subsystem error: %v", err)
	}

	// init cgroup mem config
	err = writeSubsystemFile(path.Join(subsysCgroupPath, "cpuset.mems"), []byte("0"), 0644)
	if err != nil {
		return fmt.Errorf("write cpuset.mems file error: %v", err)
	}

	err = writeSubsystemFile(path.Join(subsysCgroupPath, "cpuset.cpus"), []byte("0"), 0644)
	if err != nil {
		return fmt.Errorf("write cpuset.cpus file error: %v", err)
	}

	if res.CpuSet == "" {
		return nil
	}

	if err := writeSubsystemFile(path.Join(subsysCgroupPath, "cpuset.cpus"), []byte(res.CpuSet), 0644); err != nil {
		return fmt.Errorf("write cpuset.cpus file error: %v", err)
	}
	return nil
}

func (s *CpusetSubSystem) Remove(containerName string) error {
	if err := removeCgroupAtPath(s.Name(), containerName); err != nil {
		return fmt.Errorf("remove cgroup at path for cpuset subsystem error: %v", err)
	}
	return nil
}

func (s *CpusetSubSystem) Apply(containerName string, pid int) error {
	if err := applyPidToCgroup(s.Name(), containerName, pid, 0644); err != nil {
		return fmt.Errorf("apply pid to cgroup for cpuset subsystem error: %v", err)
	}
	return nil
}

func (s *CpusetSubSystem) Name() string {
	return "cpuset"
}
