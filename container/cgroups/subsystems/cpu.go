package subsystems

import (
	"fmt"
	"path"
)

type CpuSubSystem struct {
}

func (s *CpuSubSystem) Set(containerName string, res *ResourceConfig) error {
	subsysCgroupPath, err := GetCgroupPath(s.Name(), containerName, true)
	if err != nil {
		return err
	}

	if res.CpuShare == "" {
		return nil
	}

	return writeSubsystemFile(path.Join(subsysCgroupPath, "cpu.shares"), []byte(res.CpuShare), 0644)
}

func (s *CpuSubSystem) Remove(path string) error {
	return removeCgroupAtPath(s.Name(), path)
}

func (s *CpuSubSystem) Apply(path string, pid int) error {
	return applyPidToCgroup(s.Name(), path, pid, 0644)
}

func (s *CpuSubSystem) Name() string {
	return "cpu"
}
