# Use the official Golang image as a build environment
# Using alpine for a smaller image size
FROM golang:1.22-alpine

# Set the working directory inside the container
WORKDIR /app

# Copy go.mod and go.sum to leverage Docker's build cache
COPY go.mod go.sum ./
# Download dependencies
RUN go mod tidy

# Copy the rest of the application's source code
COPY . .

# Build the application for Linux. The output will be in /app/bin/
RUN go build -o /app/bin/mdocker entry.go 