package cmd

import (
	"docker/container/container_info"
	"docker/container/network"
	"fmt"
	"strconv"
	"syscall"

	"github.com/urfave/cli/v2"
)

var StopCommand = cli.Command{
	Name:  "stop",
	Usage: "stop a container",
	Action: func(ctx *cli.Context) error {
		// 缺少containerName
		if len(ctx.Args().Slice()) < 1 {
			return fmt.Errorf("lack of containerName")
		}

		containerName := ctx.Args().Get(0)

		return stopContainer(containerName)
	},
}

// 停止容器
func stopContainer(containerName string) error {
	// 通过containerName, 读取containerInfo获取pid
	cInfo, err := container_info.GetContainerInfoByContainerName(containerName)
	if err != nil {
		return err
	}
	pid, err := strconv.Atoi(cInfo.Pid)
	if err != nil {
		return fmt.Errorf("pid parse error, %v", err)
	}

	// 给container的init进程(pid=1的进程)发送终止信号
	if err = syscall.Kill(pid, syscall.SIGTERM); err != nil {
		return fmt.Errorf("stop container error %v", err)
	}

	// 清除容器的网络环境
	if err = network.NetworkManager.DisConnect(cInfo); err != nil {
		return fmt.Errorf("network disconnect error, %v", err)
	}

	// 清除容器fs
	if err = container_init.DeleteWorkSpace(cInfo.Name, cInfo.Volume); err != nil {
		return fmt.Errorf("delete work space error, %v", err)
	}

	// 删除cgroup
	cgroups.NewCgroupManager(cInfo.Name).Destroy()

	cInfo.Pid = ""
	cInfo.Status = container_info.StatusStop
	if err = container_info.UpdateContainerInfo(containerName, cInfo); err != nil {
		return fmt.Errorf("container info update error, %v", err)
	}

	return nil
}
