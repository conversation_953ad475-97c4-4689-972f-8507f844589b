package cmd

import (
	"docker/container/cgroups"
	"docker/container/container_info"
	"docker/container/container_init"
	"docker/container/network"
	"fmt"
	"os"

	"github.com/urfave/cli/v2"
)

var RmCommand = cli.Command{
	Name:  "rm",
	Usage: "delete a container info",
	Action: func(ctx *cli.Context) error {
		if len(ctx.Args().Slice()) < 1 {
			return fmt.Errorf("missing container name")
		}

		containerName := ctx.Args().Get(0)

		return removeContainer(containerName)
	},
}

// 删除指定name的容器
func removeContainer(containerName string) error {
	containerInfo, err := container_info.GetContainerInfoByContainerName(containerName)
	if err != nil {
		return err
	}

	if containerInfo.Status == container_info.StatusRunning {
		return fmt.Errorf("cannot remove running container")
	}

	containerInfoDir := container_info.GetContainerInfoDirPath(containerName)
	if err = os.RemoveAll(containerInfoDir); err != nil {
		return fmt.Errorf("container remove error, %v", err)
	}

	// 移除cgroup path
	cgroups.NewCgroupManager(containerName).Destroy()
	// 移除文件系统
	if err = container_init.DeleteWorkSpace(containerInfo.Name, containerInfo.Volume); err != nil {
		return err
	}

	// 清除容器网络环境
	if err = network.NetworkManager.DisConnect(containerInfo); err != nil {
		return fmt.Errorf("network disconnect error, %v", err)
	}

	return nil
}
