package cmd

import (
	"docker/container/network"
	"fmt"

	"github.com/urfave/cli/v2"
)

// mdocker network命令
var NetworkCmd = cli.Command{
	Name:  "network",
	Usage: "container network commands",
	Subcommands: []*cli.Command{
		{
			Name:  "create",
			Usage: "create a container network",
			Flags: []cli.Flag{
				&cli.StringFlag{
					Name:  "driver",
					Usage: "network driver",
				},
				&cli.StringFlag{
					Name:  "subnet",
					Usage: "subnet cidr",
				},
			},
			Action: func(ctx *cli.Context) error {
				if len(ctx.Args().Slice()) < 1 {
					return fmt.Errorf("missing network name")
				}

				err := network.NetworkManager.CreateNetwork(
					ctx.String("driver"), ctx.String("subnet"), ctx.Args().Get(0))
				if err != nil {
					return fmt.Errorf("create network error: %+v", err)
				}

				return nil
			},
		},
		{
			Name:  "list",
			Usage: "list container network",
			Action: func(ctx *cli.Context) error {
				network.NetworkManager.ListNetwork()
				return nil
			},
		},
		{
			Name:  "remove",
			Usage: "remove container network",
			Action: func(ctx *cli.Context) error {
				if len(ctx.Args().Slice()) < 1 {
					return fmt.Errorf("missing network name")
				}

				err := network.NetworkManager.DeleteNetwork(ctx.Args().Get(0))
				if err != nil {
					return fmt.Errorf("remove network error: %+v", err)
				}
				return nil
			},
		},
	},
}
