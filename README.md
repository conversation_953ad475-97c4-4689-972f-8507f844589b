# mDocker 介绍

## mdocker 是什么?

正如项目名称那样, 这是一个 mini 版的 docker 实现, 实现参考了 mydocker:

[mydocker 仓库地址](https://github.com/xianlubird/mydocker)

本项目的目的是学习 docker 的底层原理, 而不将其作作为生产工具使用。

实现中涉及使用到的 Linux 技术在我的博客中发表了相关的系列文章, 希望能给大家学习这个项目带来帮助:

[之深海](http://toseafloor.xyz)

## 项目环境准备

在编译 mDocker 之前, 你需要准备的环境有:

1. go version >= 1.22
2. ubuntu >= ubuntu 18.04, docker 依赖的 linux 机制意味着你必须拥有一个 linux 环境

## 编译二进制文件

在项目根目录下, 执行以下命令进行编译:

```shell
go mod tidy
mkdir -p ./bin
CGO_ENABLED=1 GOOS=linux GOARCH=amd64 go build -o bin/mdocker entry.go
```

# mdocker 用法

## mdocker load/save - 准备镜像

mdocker 可以运行 docker 镜像, 比如我们将 docker 的 busybox 镜像导出:

```shell
docker pull busybox:latest
docker run -d busybox top
docker export -o busybox.tar [刚才启动容器id]
```

将 busybox 镜像导入 mdocker:

```shell
mdocker load ./busybox.tar busybox
```

## mdocker run - 启动一个容器

创建一个 sh 交互式容器:

```shell
mdocker run -ti --name sample busybox sh
```

创建一个后台运行的容器:

```shell
mdocker run -d --name sample busybox top
```

创建一个带资源限制的容器:

```shell
mdocker run -ti --name sample -m 100m --cpushare 512 --cpuset 0,1 busybox sh
```

创建一个带卷挂载的容器:

```shell
mdocker run -ti --name sample -v /host/path:/container/path busybox sh
```

## mdocker network

创建一个 bridge 网络:

```shell
mdocker network create --driver bridge --subnet ************/24 mdocker0
```

查看所有网络:

```shell
mdocker network list
```

删除网络:

```shell
mdocker network remove mdocker0
```

创建容器时加入指定网络:

```shell
mdocker run -ti --name sample --net mdocker0 busybox sh
```

## mdocker ps - 查看容器列表

```shell
mdocker ps
```

## mdocker logs - 查看容器日志

```shell
mdocker logs sample
```

## mdocker exec - 在容器中执行命令

```shell
mdocker exec sample sh
```

## mdocker stop - 停止容器

```shell
mdocker stop sample
```

## mdocker rm - 删除容器

```shell
mdocker rm sample
```
