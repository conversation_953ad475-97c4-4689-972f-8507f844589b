package main

import (
	_ "docker/boot"
	"docker/cmd"
	"docker/config"
	"docker/utils"
	"os"

	"github.com/urfave/cli/v2"
)

func main() {
	app := &cli.App{
		Name:  config.AppName,
		Usage: config.Usage,
		Commands: []*cli.Command{
			&cmd.RunCommand,
			&cmd.InitCmd,
			&cmd.SaveCommand,
			&cmd.LoadCommand,
			&cmd.ListCommand,
			&cmd.LogCommand,
			&cmd.ExecCommand,
			&cmd.StopCommand,
			&cmd.RmCommand,
			&cmd.NetworkCmd,
		},
	}

	if err := app.Run(os.Args); err != nil {
		utils.LoggerUtil.Fatalf(err.Error())
	}
}
